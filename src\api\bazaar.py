import requests as re
from colorama import Fore, Style

def get_bazaar_data():
    try:
        response = re.get('https://api.hypixel.net/v2/skyblock/bazaar')
        response.raise_for_status()
        return response.json()
    except re.exceptions.RequestException as e:
        print(f'Error fetching bazaar data: {e}')
        return None
    
def extract_shard_prices(bazaar_data):
    """Extract buy and sell prices for all shards"""
    shard_prices = {}
    
    if not bazaar_data or "products" not in bazaar_data:
        print(f"{Fore.RED}Invalid bazaar data format{Style.RESET_ALL}")
        return None
    
    for product_id, product_data in bazaar_data["products"].items():
        if product_id.startswith("SHARD_"):
            quick_status = product_data.get("quick_status", {})
            shard_prices[product_id] = {
                "buy_price": quick_status.get("buyPrice", 0),
                "sell_price": quick_status.get("sellPrice", 0),
                "weekly_buy": quick_status.get("buyMovingWeek", 0)
            }
    
    return shard_prices