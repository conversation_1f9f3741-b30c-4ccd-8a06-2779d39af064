from colorama import Fore, Style

def display_fusions(profits, shard_prices, prctsold=5):
    """Display normal fusion profits"""
    print(f"\n{Fore.CYAN}=== Hypixel Skyblock Fusion Shard Profit Calculator ===")
    print("=" * 110 + Style.RESET_ALL)
    
    for shard, data in profits.items():
        if data['profit_insta'] < 0 and data['profit_order'] < 0:
            continue
        if data['profit_percent_insta'] < prctsold and data['profit_percent_order'] < prctsold:
            continue
    
        material_strings = []
        for m, q in data["materials"].items():
            # Split on first underscore after "SHARD_" and take the rest
            if m.startswith("SHARD_"):
                material_name = m[6:]  # Takes everything after "SHARD_"
            else:
                material_name = m  # Fallback for non-prefixed items
            material_strings.append(f"{q}x {material_name}")
        
        materials = ", ".join(material_strings)
        
        # Display the shard name (without SHARD_ prefix)
        display_name = shard[6:] if shard.startswith("SHARD_") else shard
        
        print(
            f"{Fore.YELLOW}┌── {display_name:<12} "
            f"{Fore.WHITE}│ Materials: {Fore.CYAN}{materials[:70]}{Style.RESET_ALL}"
        )
        
        def color_profit(val, percent):
            arrow = "↑" if val >= 0 else "↓"
            color = Fore.GREEN if val >= 0 else Fore.RED
            return f"{color}{arrow} {abs(val):,.1f} [{abs(percent):.1f}%]{Style.RESET_ALL}"
        
        print(
            f"{Fore.YELLOW}└───{Style.RESET_ALL} "
            f"Cost: {Fore.CYAN}{data['total_cost']:,.1f}{Style.RESET_ALL} | "
            f"Revenue: {Fore.MAGENTA}{data['total_revenue_insta']:,.1f}{Style.RESET_ALL}(I) / "
            f"{Fore.MAGENTA}{data['total_revenue_order']:,.1f}{Style.RESET_ALL}(O) | "
            f"Profit: {color_profit(data['profit_insta'], data['profit_percent_insta'])}(I) / "
            f"{color_profit(data['profit_order'], data['profit_percent_order'])}(O) | "
            f"Weekly: {Fore.YELLOW}{shard_prices[shard].get('weekly_buy', 0):,}{Style.RESET_ALL}"
        )
        print(f"{Fore.YELLOW}─" * 120 + Style.RESET_ALL)

    print(f"\n{Fore.WHITE}Key: (I)=Instant Sell, (O)=Buy Order | ↑=Profit ↓=Loss{Style.RESET_ALL}")