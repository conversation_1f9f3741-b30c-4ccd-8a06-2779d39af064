from colorama import Style, Fore

def display_shard_orders(shard_name, bazaar_data):
    """Display buy and sell orders for a specific shard in a formatted way"""
    if shard_name not in bazaar_data.get("products", {}):
        print(f"{Fore.RED}Shard {shard_name} not found in bazaar data{Style.RESET_ALL}")
        return
    
    product_data = bazaar_data["products"][shard_name]
    display_name = shard_name.split('_')[-1]
    
    print(f"\n{Fore.CYAN}=== {display_name} Order Book ===")
    print("=" * 110 + Style.RESET_ALL)
    
    # Display sell orders (cheapest first)
    print(f"\n{Fore.RED}┌── Sell Orders (Lowest to Highest){Style.RESET_ALL}")
    for i, order in enumerate(product_data.get("sell_summary", [])[:10]):  # Show top 10
        print(
            f"{Fore.RED}│{Fore.WHITE} {i+1}. {order['amount']:>4}x at {Fore.YELLOW}{order['pricePerUnit']:,.1f}{Fore.WHITE} "
            f"(Total: {Fore.YELLOW}{order['amount'] * order['pricePerUnit']:,.1f}{Fore.WHITE})"
        )
    
    # Display buy orders (highest first)
    print(f"\n{Fore.GREEN}┌── Buy Orders (Highest to Lowest){Style.RESET_ALL}")
    for i, order in enumerate(product_data.get("buy_summary", [])[:10]):  # Show top 10
        print(
            f"{Fore.GREEN}│{Fore.WHITE} {i+1}. {order['amount']:>4}x at {Fore.YELLOW}{order['pricePerUnit']:,.1f}{Fore.WHITE} "
            f"(Total: {Fore.YELLOW}{order['amount'] * order['pricePerUnit']:,.1f}{Fore.WHITE})"
        )
    
    print(f"\n{Fore.CYAN}Quick Status:{Style.RESET_ALL}")
    quick_status = product_data.get("quick_status", {})
    print(
        f"Buy Price: {Fore.GREEN}{quick_status.get('buyPrice', 0):,.1f}{Style.RESET_ALL} | "
        f"Sell Price: {Fore.RED}{quick_status.get('sellPrice', 0):,.1f}{Style.RESET_ALL} | "
        f"Buy Volume: {Fore.YELLOW}{quick_status.get('buyMovingWeek', 0):,}{Style.RESET_ALL}"
    )