from colorama import Style, Fore

def display_chameleon_profits(profits):
    """Display chameleon fusion profits with output shard details (only profitable outputs)"""
    print(f"\n{Fore.CYAN}=== Hypixel Skyblock Chameleon Fusion Profit Calculator ===")
    print("=" * 110 + Style.RESET_ALL)
    
    for input_shard, data in profits.items():
        # Handle input shard name (remove SHARD_ prefix if present)
        if input_shard.startswith("SHARD_"):
            input_display_name = input_shard[6:]  # Remove "SHARD_" prefix
        else:
            input_display_name = input_shard  # Use as-is for unmapped shards
        
        # Filter to only show outputs with at least one positive profit
        profitable_outputs = [
            output for output in data["outputs"] 
            if output["profit_insta"] > 0 or output["profit_order"] > 0
        ]
        
        # Skip this input shard entirely if no profitable outputs
        if not profitable_outputs:
            continue
            
        print(f"\n{Fore.YELLOW}┌── {input_display_name:<15} (Input Cost: {Fore.CYAN}{data['input_price']:,.1f}{Fore.YELLOW}){Style.RESET_ALL}")
        
        for output in profitable_outputs:
            # Handle output shard name (remove SHARD_ prefix if present)
            if output["name"].startswith("SHARD_"):
                output_display_name = output["name"][6:]  # Remove "SHARD_" prefix
            else:
                output_display_name = output["name"]  # Use as-is for unmapped shards
            
            def color_profit(val, percent):
                arrow = "↑" if val >= 0 else "↓"
                color = Fore.GREEN if val >= 0 else Fore.RED
                return f"{color}{arrow} {abs(val):,.1f} [{abs(percent):.1f}%]{Style.RESET_ALL}"
            
            # Only show the profit if it's positive
            insta_display = (
                f"{Fore.WHITE}│ Insta: {Fore.MAGENTA}{output['sell_price']:,.1f}{Fore.YELLOW} "
                f"(Profit: {color_profit(output['profit_insta'], output['profit_percent_insta'])}) "
                if output["profit_insta"] > 0 else ""
            )
            
            order_display = (
                f"{Fore.WHITE}│ Order: {Fore.MAGENTA}{output['buy_price']:,.1f}{Fore.WHITE} "
                f"(Profit: {color_profit(output['profit_order'], output['profit_percent_order'])})"
                if output["profit_order"] > 0 else ""
            )
            
            print(
                f"{Fore.YELLOW}├─ {Fore.WHITE}{output_display_name:<15} "
                f"{insta_display}"
                f"{order_display}"
                f"{Fore.WHITE} │ Weekly: {Fore.CYAN}{output['weekly_volume']:,}{Style.RESET_ALL}"
            )
        
        print(f"{Fore.YELLOW}└{'─' * 100}{Style.RESET_ALL}")

    print(f"\n{Fore.WHITE}Key: ↑=Profit ↓=Loss | Only showing profitable outputs{Style.RESET_ALL}")