from utils.utils import get_base_materials

def calculate_total_cost(target_shard, shard_prices, recipes):
    """Calculate total cost from base materials to final product"""
    if target_shard not in recipes:
        return None
    
    total_cost = 0
    materials_needed = get_base_materials(target_shard, recipes)
    
    for material, quantity in materials_needed.items():
        if material in shard_prices:
            total_cost += shard_prices[material]["buy_price"] * quantity
        else:
            return None
    
    return total_cost

def calculate_profits(shard_prices, recipes):
    profits = {}
    for result_shard, recipe_data in recipes.items():
        # Skip if shard not in bazaar data
        if result_shard not in shard_prices:
            continue
            
        total_cost = calculate_total_cost(result_shard, shard_prices, recipes)
        if total_cost is None:
            continue
            
        output_quantity = recipe_data["output"]
        total_revenue_sell_order = shard_prices[result_shard]["buy_price"] * output_quantity
        total_revenue_sell_insta = shard_prices[result_shard]['sell_price'] * output_quantity

        profit_order = total_revenue_sell_order - total_cost
        profit_insta = total_revenue_sell_insta - total_cost
        profit_insta_percent = (profit_insta / total_cost) * 100 if total_cost != 0 else 0
        profit_order_percent = (profit_order / total_cost) * 100 if total_cost != 0 else 0
        
        profits[result_shard] = {
            "total_cost": total_cost,
            "total_revenue_order": total_revenue_sell_order,
            "total_revenue_insta": total_revenue_sell_insta,
            "profit_insta": profit_insta,
            "profit_order": profit_order,
            "profit_percent_insta": profit_insta_percent,
            "profit_percent_order": profit_order_percent,
            "output_quantity": output_quantity,
            "materials": get_base_materials(result_shard, recipes)
        }
    
    return dict(sorted(
        profits.items(),
        key=lambda x: x[1]["profit_percent_order"],
        reverse=True
    ))