from colorama import init, Fore, Style
from prompt_toolkit import prompt
from prompt_toolkit.completion import WordCompleter

from display.display_chameleon import display_chameleon_profits
from display.display_fusions import display_fusions
from display.display_orders import display_shard_orders

from calculations.chameleon_fusions import calculate_chameleon_profits
from calculations.normal_fusions import calculate_profits
from calculations.simulation import simulate_craft


from api.bazaar import get_bazaar_data, extract_shard_prices

from utils.utils import FUSION_RECIPES, SHARD_MAPPINGS, CHAMELEON_RECIPES, extract_shards


# Initialize colorama
init()

def main_menu():
    """Display main menu and handle user choice"""
    print(f"\n{Fore.CYAN}=== Hypixel Skyblock Fusion Calculator ===")
    print(f"{Fore.YELLOW}1. Normal Fusion Profits")
    print(f"2. Chameleon Fusion Profits")
    print(f"3. View Shard Order Book")
    print(f"4. Simulate Craft")
    print(f"5. Exit{Style.RESET_ALL}")
    
    while True:
        choice = input(f"{Fore.BLUE}Select an option (1-5): {Style.RESET_ALL}").strip()
        if choice in ["1", "2", "3", "4", "5"]:
            return int(choice)
        print(f"{Fore.RED}Invalid choice. Please enter 1, 2, 3, 4 or 5 {Style.RESET_ALL}")

def main():
    # Check data loaded properly
    if not FUSION_RECIPES or not CHAMELEON_RECIPES or not SHARD_MAPPINGS:
        print(f"{Fore.RED}Failed to load required data files{Style.RESET_ALL}")
        return

    while True:
        choice = main_menu()
        shard_complete = extract_shards(SHARD_MAPPINGS)
        completer = WordCompleter(shard_complete, ignore_case=True, sentence=True)

        if choice in [1, 2, 3, 4]:
            print(f"{Fore.BLUE}Fetching bazaar data from Hypixel API...{Style.RESET_ALL}")
            bazaar_data = get_bazaar_data()
            if not bazaar_data:
                continue  # Skip this iteration if API fails
                
            # Extract shard prices
            shard_prices = extract_shard_prices(bazaar_data)
            if not shard_prices:
                print(f"{Fore.RED}No shard price data found{Style.RESET_ALL}")
                continue
        
        if choice == 1:
            # Normal fusions
            print(f"{Fore.BLUE}Calculating normal fusion profits...{Style.RESET_ALL}")
            profits = calculate_profits(shard_prices, FUSION_RECIPES)
            if profits:
                display_fusions(profits, shard_prices, prctsold=20)
            else:
                print(f"{Fore.RED}No valid profit calculations available{Style.RESET_ALL}")
                
        elif choice == 2:
            # Chameleon fusions
            print(f"{Fore.BLUE}Calculating chameleon fusion profits...{Style.RESET_ALL}")
            profits = calculate_chameleon_profits(shard_prices, CHAMELEON_RECIPES, SHARD_MAPPINGS)
            if profits:
                display_chameleon_profits(profits)
            else:
                print(f"{Fore.RED}No valid chameleon profit calculations available{Style.RESET_ALL}")
                
        elif choice == 3:
            # Shard order book
            shard_name = prompt("Enter shard name (e.g., SEER, CHAMELEON): ", completer=completer).strip().upper()
            if not shard_name.startswith("SHARD_"):
                shard_name = f"SHARD_{shard_name}"
            display_shard_orders(shard_name, bazaar_data)

        elif choice == 4:
            #Simulation
            target = prompt("Enter shard name (e.g., SEER, CHAMELEON): ", completer=completer).strip().upper()
            quan = int(input(f"{Fore.BLUE}Enter quantity: {Style.RESET_ALL}"))
            if not target.startswith("SHARD_"):
                target = f"SHARD_{target}"
            simulate_craft(target, quan, shard_prices, FUSION_RECIPES)
            
        elif choice == 5:
            print(f"{Fore.GREEN}Exiting...{Style.RESET_ALL}")
            break

if __name__ == "__main__":
    main()