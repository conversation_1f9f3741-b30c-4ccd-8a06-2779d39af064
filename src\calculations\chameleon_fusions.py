def calculate_chameleon_profits(shard_prices, chameleon_recipes, mappings):
    """Calculate profits from chameleon fusions with detailed output info"""
    profits = {}
    
    for input_shard, recipe_data in chameleon_recipes.items():
        # Get the SHARD_ name from mappings
        input_shard_name = mappings.get(input_shard, f"SHARD_{input_shard}")
        
        # Skip if input shard not in bazaar
        if input_shard_name not in shard_prices:
            continue
            
        # Each chameleon fusion requires 2 chameleon shards + the input shard
        chameleon_price = shard_prices.get("SHARD_CHAMELEON", {}).get("buy_price", 0)
        input_price = (5 * shard_prices[input_shard_name]["buy_price"] ) + (2 * chameleon_price)
        
        output_shards = recipe_data["chameleon_outputs"]
        
        # Calculate profit for each possible output
        output_details = []
        for output_shard in output_shards:
            output_shard_name = mappings.get(output_shard, f"SHARD_{output_shard}")
            if output_shard_name in shard_prices:
                sell_price = shard_prices[output_shard_name]["sell_price"]
                buy_price = shard_prices[output_shard_name]["buy_price"]
                profit_insta = sell_price - input_price
                profit_order = buy_price - input_price
                weekly_volume = shard_prices[output_shard_name].get("weekly_buy", 0)
                
                output_details.append({
                    "name": output_shard_name,
                    "sell_price": sell_price,
                    "buy_price": buy_price,
                    "profit_insta": profit_insta,
                    "profit_order": profit_order,
                    "profit_percent_insta": (profit_insta / input_price) * 100 if input_price != 0 else 0,
                    "profit_percent_order": (profit_order / input_price) * 100 if input_price != 0 else 0,
                    "weekly_volume": weekly_volume
                })
                
        if not output_details:
            continue
            
        profits[input_shard_name] = {
            "input_price": input_price,
            "outputs": output_details,
            "chameleon_cost": 2 * chameleon_price  # Store this separately for display if needed
        }
    
    return dict(sorted(
        {k: v for k, v in profits.items() 
        if any(out["profit_order"] > 0 for out in v["outputs"])}.items(),
        key=lambda x: max([out["profit_order"] for out in x[1]["outputs"]]),
        reverse=True
    ))