from colorama import Fore, Style
from utils.utils import FUSION_RECIPES
from api.bazaar import extract_shard_prices, get_bazaar_data
from math import ceil
from utils.utils import get_base_materials

def simulate_craft(target_shard, quantity=1, shard_prices=None, recipes=FUSION_RECIPES):
    """
    Simulate crafting a shard to see costs and profits, including full recipe tree.
    
    Args:
        target_shard (str): Shard ID to craft (e.g., "SHARD_FIERCE").
        quantity (int): How many crafts to simulate.
        shard_prices (dict): Bazaar prices (if None, fetches fresh data).
        recipes (dict): Your fusion recipes.
    """
    if not shard_prices:
        shard_prices = extract_shard_prices(get_bazaar_data())
    
    if target_shard not in recipes:
        print(f"{Fore.RED}Error: {target_shard} is not a valid fusion shard.{Style.RESET_ALL}")
        return
    
    # Calculate full recipe tree with proper rounding
    def get_recipe_tree(shard, qty):
        tree = {
            "shard": shard,
            "quantity": qty,
            "crafts_needed": 1,
            "ingredients": [],
            "is_base": False,
            "total_cost": 0
        }
        
        if shard not in recipes:
            tree["is_base"] = True
            tree["total_cost"] = shard_prices.get(shard, {}).get("buy_price", 0) * qty
            return tree
        
        # Calculate how many crafts are needed (rounded up)
        tree["crafts_needed"] = ceil(qty / recipes[shard]["output"])
        output_qty = recipes[shard]["output"] * tree["crafts_needed"]
        
        for ingredient, ing_qty in recipes[shard]["ingredients"]:
            required_qty = ing_qty * tree["crafts_needed"]
            ingredient_tree = get_recipe_tree(ingredient, required_qty)
            tree["ingredients"].append(ingredient_tree)
            tree["total_cost"] += ingredient_tree["total_cost"]
        
        return tree
    
    recipe_tree = get_recipe_tree(target_shard, quantity)
    materials_needed = get_base_materials(target_shard, recipes, quantity)
    
    # Calculate costs using base materials (your existing accurate method)
    total_cost = sum(
        shard_prices[material]["buy_price"] * qty
        for material, qty in materials_needed.items()
        if material in shard_prices
    )
    
    # Calculate revenue
    output_qty = recipes[target_shard]["output"] * recipe_tree["crafts_needed"]
    revenue_insta = shard_prices[target_shard]["sell_price"] * output_qty
    revenue_order = shard_prices[target_shard]["buy_price"] * output_qty
    
    # Profit calculations
    profit_insta = revenue_insta - total_cost
    profit_order = revenue_order - total_cost
    margin_insta = (profit_insta / total_cost) * 100 if total_cost != 0 else 0
    margin_order = (profit_order / total_cost) * 100 if total_cost != 0 else 0
    
    # Display results
    print(f"\n{Fore.CYAN}=== Craft Simulation: {target_shard} x{quantity} ===")
    print("=" * 60 + Style.RESET_ALL)
    
    # Print recipe tree with crafting information
    def print_tree(node, level=0, is_last=True):
        prefix = "    " * level
        connector = "└── " if is_last else "├── "
        
        if node["is_base"]:
            cost = shard_prices.get(node["shard"], {}).get("buy_price", 0) * node["quantity"]
            print(f"{prefix}{connector}{Fore.BLUE}{node['shard']}{Style.RESET_ALL} x{node['quantity']:,}{Style.RESET_ALL}")
        else:
            # Highlighted craft information
            #craft_info = f"{Fore.MAGENTA} [Crafts: {node['crafts_needed']} → Makes: {recipes[node['shard']]['output']} × {node['crafts_needed']} = {node['crafts_needed'] * recipes[node['shard']]['output']}]{Style.RESET_ALL}"
            main_line = f"{prefix}{connector}{Fore.GREEN}{node['shard']}{Style.RESET_ALL} x{node['quantity']:,}"
            #cost_info = f" (Total: {Fore.CYAN}{node['total_cost']:,.1f} coins{Style.RESET_ALL})"
            
            # Print main line and craft info on same line with different colors
            print(main_line)
            
            for i, child in enumerate(node["ingredients"]):
                print_tree(child, level + 1, i == len(node["ingredients"]) - 1)
    
    print(f"{Fore.YELLOW}Recipe Tree:{Style.RESET_ALL}")
    print_tree(recipe_tree)
    
    # Materials breakdown (base materials only)
    print(f"\n{Fore.YELLOW}Base Materials Needed:{Style.RESET_ALL}")
    for material, qty in materials_needed.items():
        cost = shard_prices[material]["buy_price"] * qty
        print(f"  - {qty:,}x {Fore.BLUE}{material}{Style.RESET_ALL} = {Fore.CYAN}{cost:,.1f} coins{Style.RESET_ALL}")
    
    # Summary
    print(f"\n{Fore.YELLOW}Summary:{Style.RESET_ALL}")
    print(f"  - Total Crafts Needed: {recipe_tree['crafts_needed']}")
    print(f"  - Total Cost: {Fore.CYAN}{total_cost:,.1f} coins{Style.RESET_ALL}")
    print(f"  - Output: {Fore.GREEN}{output_qty}x {target_shard}{Style.RESET_ALL}")
    print(f"  - Revenue (Instant Sell): {Fore.CYAN}{revenue_insta:,.1f} coins{Style.RESET_ALL}")
    print(f"  - Revenue (Buy Order): {Fore.CYAN}{revenue_order:,.1f} coins{Style.RESET_ALL}")
    
    # Profit highlights
    def colorize(profit):
        return f"{Fore.GREEN if profit >= 0 else Fore.RED}{profit:+,.1f}{Style.RESET_ALL}"
    
    print(f"\n{Fore.YELLOW}Profit:{Style.RESET_ALL}")
    print(f"  - Instant Sell: {colorize(profit_insta)} coins ({colorize(margin_insta)}%)")
    print(f"  - Buy Order: {colorize(profit_order)} coins ({colorize(margin_order)}%)")
    
    # Recommendation
    if profit_insta > 0:
        print(f"\n{Fore.GREEN}✅ Profitable! Crafting is worth it.{Style.RESET_ALL}")
    else:
        print(f"\n{Fore.RED}❌ Not profitable. Avoid crafting.{Style.RESET_ALL}")