from math import ceil
from pathlib import Path
from colorama import Style, Fore
import json

def get_base_materials(target_shard, recipes, needed_quantity=1):
    """Always calculates base materials, ignoring intermediate steps"""
    base_materials = {}
    
    def _recurse(shard, quantity):
        if shard in recipes:
            recipe = recipes[shard]
            crafts_needed = ceil(quantity / recipe["output"])
            for ingredient, qty in recipe["ingredients"]:
                _recurse(ingredient, qty * crafts_needed)
        else:
            base_materials[shard] = base_materials.get(shard, 0) + quantity
    
    _recurse(target_shard, needed_quantity)
    return base_materials

def extract_shards(data): 
    suff = list()
    for value in data.values():
        if value.startswith("SHARD_"):
            suffix = value[6:]
            suff.append(suffix.upper())
    return suff

# Load data files
def load_json_data(json_path):
    try:
        with open(json_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"{Fore.RED}Error loading {json_path}: {e}{Style.RESET_ALL}")
        return None
    
# Paths to data files
DATA_DIR = Path(__file__).parent.parent.parent / 'data'
RECIPES_FILE = DATA_DIR / 'fusions.json'
CHAMELEON_FILE = DATA_DIR / 'chameleon.json'
MAPPINGS_FILE = DATA_DIR / 'mappings.json'

# Load data
FUSION_RECIPES = load_json_data(RECIPES_FILE)
CHAMELEON_RECIPES = load_json_data(CHAMELEON_FILE)
SHARD_MAPPINGS = load_json_data(MAPPINGS_FILE)
